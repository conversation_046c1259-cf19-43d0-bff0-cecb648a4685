import React, { useEffect, useMemo, useState } from "react";

// نموذج أولي بواجهة واحدة قابلة للمعاينة
// يحوي: تبويب المواطن + لوحة مكتبك + لوحة المكتب المندلاوي
// ملاحظات:
// - البيانات تحفظ محلّيًا عبر localStorage للتجربة.
// - إشعارات فورية مبسّطة (تظهر كبطاقة أعلى يمين الشاشة).
// - يمكنك تعديل الحالة وملاحظة تتبّع الحالة وخط الزمن.

// أدوات مساعدة صغيرة
const uid = () => Math.random().toString(36).slice(2, 9);
const nowISO = () => new Date().toISOString();
const fmt = (iso) => new Date(iso).toLocaleString();

const STATUSES = [
  "مستلم",
  "قيد المراجعة",
  "مطلوب تعديل",
  "محوّل للمكتب المندلاوي",
  "منجز",
  "مرفوض",
];

const TYPE_OPTIONS = [
  "طلب كتاب تأييد",
  "معاملة نقل",
  "شكوى مواطن",
  "طلب مقابلة",
  "أخرى",
];

const STORAGE_KEY = "citizen-office-requests-v1";

export default function CitizenOfficePrototype() {
  const [tab, setTab] = useState("citizen"); // citizen | office | mandalawi
  const [requests, setRequests] = useState(() => {
    try {
      const raw = localStorage.getItem(STORAGE_KEY);
      return raw ? JSON.parse(raw) : [];
    } catch {
      return [];
    }
  });
  const [toasts, setToasts] = useState([]);

  useEffect(() => {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(requests));
  }, [requests]);

  // إشعار مبسّط
  const pushToast = (title, body) => {
    const id = uid();
    setToasts((t) => [{ id, title, body }, ...t]);
    setTimeout(() => setToasts((t) => t.filter((x) => x.id !== id)), 5500);
  };

  // إنشاء طلب جديد من المواطن
  const createRequest = (payload) => {
    const id = uid();
    const rec = {
      id,
      citizenName: payload.citizenName,
      phone: payload.phone,
      type: payload.type,
      title: payload.title,
      details: payload.details,
      status: "مستلم",
      assignedTo: "مكتبك",
      createdAt: nowISO(),
      updatedAt: nowISO(),
      history: [
        { at: nowISO(), status: "مستلم", by: "النظام", note: "تم استلام الطلب" },
      ],
    };
    setRequests((prev) => [rec, ...prev]);
    pushToast("تم استلام طلبك", `الرمز: ${id} — يمكنك متابعة الحالة من لوحة المواطن`);
  };

  // تحديث حالة الطلب
  const updateStatus = (id, status, by, note = "") => {
    setRequests((prev) =>
      prev.map((r) =>
        r.id === id
          ? {
              ...r,
              status,
              updatedAt: nowISO(),
              history: [
                { at: nowISO(), status, by, note },
                ...r.history,
              ],
            }
          : r
      )
    );
    pushToast("تحديث حالة", `الطلب ${id} أصبح \"${status}\"`);
  };

  // تحويل الطلب إلى المكتب المندلاوي
  const transferToMandalawi = (id, by) => {
    updateStatus(id, "محوّل للمكتب المندلاوي", by, "تم التحويل للمكتب المندلاوي");
    setRequests((prev) =>
      prev.map((r) => (r.id === id ? { ...r, assignedTo: "المكتب المندلاوي" } : r))
    );
  };

  return (
    <div dir="rtl" className="min-h-screen bg-gradient-to-br from-emerald-50 to-white text-slate-900">
      {/* رأس الصفحة */}
      <header className="sticky top-0 z-40 backdrop-blur bg-white/70 border-b">
        <div className="max-w-6xl mx-auto px-4 py-3 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-2xl bg-emerald-500 grid place-content-center text-white font-bold shadow">
              م
            </div>
            <div>
              <h1 className="text-lg font-semibold">منصة ربط المواطن بالمكتب</h1>
              <p className="text-xs text-slate-500">نموذج أولي للتجربة</p>
            </div>
          </div>

          <nav className="flex items-center gap-2">
            <TabButton active={tab === "citizen"} onClick={() => setTab("citizen")}>
              بوابة المواطن
            </TabButton>
            <TabButton active={tab === "office"} onClick={() => setTab("office")}>
              لوحة مكتبك
            </TabButton>
            <TabButton active={tab === "mandalawi"} onClick={() => setTab("mandalawi")}>
              لوحة المكتب المندلاوي
            </TabButton>
          </nav>
        </div>
      </header>

      {/* منطقة الإشعارات */}
      <div className="fixed right-4 top-20 z-50 space-y-2 max-w-sm">
        {toasts.map((t) => (
          <div key={t.id} className="rounded-2xl shadow border bg-white p-3">
            <div className="font-semibold">{t.title}</div>
            <div className="text-sm text-slate-600">{t.body}</div>
          </div>
        ))}
      </div>

      {/* المحتوى */}
      <main className="max-w-6xl mx-auto px-4 py-8">
        {tab === "citizen" && (
          <CitizenPortal requests={requests} onCreate={createRequest} />
        )}
        {tab === "office" && (
          <OfficeDashboard
            title="لوحة مكتبك"
            requests={requests.filter((r) => r.assignedTo === "مكتبك")}
            onUpdate={updateStatus}
            onTransfer={transferToMandalawi}
            actor="مكتبك"
          />
        )}
        {tab === "mandalawi" && (
          <OfficeDashboard
            title="لوحة المكتب المندلاوي"
            requests={requests.filter((r) => r.assignedTo === "المكتب المندلاوي")}
            onUpdate={updateStatus}
            onTransfer={() => {}}
            actor="المكتب المندلاوي"
            hideTransfer
          />
        )}
      </main>

      {/* تذييل */}
      <footer className="border-t py-6 text-center text-xs text-slate-500">
        نسخة تجريبية – يمكن التوسعة لاحقًا لإضافات (تسجيل دخول، صلاحيات، SMS، بريد، FCM)
      </footer>
    </div>
  );
}

function TabButton({ active, children, onClick }) {
  return (
    <button
      onClick={onClick}
      className={
        "px-3 py-2 rounded-xl text-sm transition shadow-sm border " +
        (active
          ? "bg-emerald-600 text-white border-emerald-600"
          : "bg-white hover:bg-emerald-50 text-slate-700 border-slate-200")
      }
    >
      {children}
    </button>
  );
}

function CitizenPortal({ requests, onCreate }) {
  const [form, setForm] = useState({
    citizenName: "",
    phone: "",
    type: TYPE_OPTIONS[0],
    title: "",
    details: "",
  });

  const myRequests = useMemo(() => {
    // فلترة بسيطة حسب رقم الهاتف إن وُجد
    if (!form.phone) return requests;
    return requests.filter((r) => r.phone.includes(form.phone));
  }, [requests, form.phone]);

  const submit = (e) => {
    e.preventDefault();
    if (!form.citizenName || !form.phone || !form.title) return alert("يرجى إكمال الحقول الأساسية");
    onCreate(form);
    setForm({ citizenName: "", phone: form.phone, type: TYPE_OPTIONS[0], title: "", details: "" });
  };

  return (
    <div className="grid md:grid-cols-2 gap-6">
      <div className="rounded-2xl border bg-white p-5 shadow-sm">
        <h2 className="text-lg font-semibold mb-4">تقديم طلب جديد</h2>
        <form onSubmit={submit} className="space-y-3">
          <Field label="الاسم الكامل">
            <input
              className="input"
              placeholder="مثال: علي حسن"
              value={form.citizenName}
              onChange={(e) => setForm({ ...form, citizenName: e.target.value })}
            />
          </Field>
          <Field label="رقم الهاتف">
            <input
              className="input"
              placeholder="07xx xxx xxxx"
              value={form.phone}
              onChange={(e) => setForm({ ...form, phone: e.target.value })}
            />
          </Field>
          <Field label="نوع الطلب">
            <select
              className="input"
              value={form.type}
              onChange={(e) => setForm({ ...form, type: e.target.value })}
            >
              {TYPE_OPTIONS.map((t) => (
                <option key={t} value={t}>
                  {t}
                </option>
              ))}
            </select>
          </Field>
          <Field label="عنوان موجز">
            <input
              className="input"
              placeholder="مثال: طلب كتاب تأييد للدائرة"
              value={form.title}
              onChange={(e) => setForm({ ...form, title: e.target.value })}
            />
          </Field>
          <Field label="تفاصيل إضافية">
            <textarea
              className="input min-h-[96px]"
              placeholder="أدخل وصفًا مختصرًا لوضعك والوثائق المساندة"
              value={form.details}
              onChange={(e) => setForm({ ...form, details: e.target.value })}
            />
          </Field>
          <div className="pt-2">
            <button className="btn-primary" type="submit">
              إرسال الطلب
            </button>
          </div>
        </form>
      </div>

      <div className="rounded-2xl border bg-white p-5 shadow-sm">
        <h2 className="text-lg font-semibold mb-2">متابعة طلباتي</h2>
        <p className="text-xs text-slate-500 mb-4">
          أدخل رقم هاتفك في النموذج الأيسر لعرض الطلبات المرتبطة بك.
        </p>
        {myRequests.length === 0 ? (
          <Empty title="لا توجد طلبات بعد" subtitle="أرسل طلبك الأول من النموذج" />
        ) : (
          <div className="space-y-3">
            {myRequests.map((r) => (
              <RequestCard key={r.id} r={r} />
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

function OfficeDashboard({ title, requests, onUpdate, onTransfer, actor, hideTransfer }) {
  const [q, setQ] = useState("");
  const filtered = useMemo(() => {
    return requests.filter(
      (r) =>
        r.id.includes(q) ||
        r.citizenName.includes(q) ||
        r.phone.includes(q) ||
        r.title.includes(q)
    );
  }, [q, requests]);

  return (
    <div className="space-y-4">
      <div className="flex items-end justify-between gap-3 flex-wrap">
        <div>
          <h2 className="text-xl font-bold">{title}</h2>
          <p className="text-xs text-slate-500">الطلبات المعروضة: {filtered.length}</p>
        </div>
        <input
          className="input w-full md:w-80"
          placeholder="بحث بالاسم / الهاتف / العنوان / رقم الطلب"
          value={q}
          onChange={(e) => setQ(e.target.value)}
        />
      </div>

      <div className="grid md:grid-cols-2 xl:grid-cols-3 gap-4">
        {filtered.length === 0 ? (
          <div className="md:col-span-2 xl:col-span-3">
            <Empty title="لا توجد سجلات" subtitle="جرّب تعديل البحث" />
          </div>
        ) : (
          filtered.map((r) => (
            <div key={r.id} className="rounded-2xl border bg-white p-4 shadow-sm">
              <div className="flex items-center justify-between gap-2">
                <div className="font-semibold">{r.title}</div>
                <span className="text-[11px] px-2 py-1 rounded-full border bg-emerald-50 text-emerald-700">
                  {r.status}
                </span>
              </div>
              <div className="text-xs text-slate-500 mt-1">رمز الطلب: {r.id}</div>
              <div className="mt-3 grid grid-cols-2 gap-2 text-sm">
                <Info label="المواطن" value={r.citizenName} />
                <Info label="الهاتف" value={r.phone} />
                <Info label="النوع" value={r.type} />
                <Info label="لدى" value={r.assignedTo} />
                <Info label="أُنشئ" value={fmt(r.createdAt)} />
                <Info label="آخر تحديث" value={fmt(r.updatedAt)} />
              </div>

              <div className="mt-4 flex flex-wrap items-center gap-2">
                <select
                  className="input"
                  value={r.status}
                  onChange={(e) => onUpdate(r.id, e.target.value, actor)}
                >
                  {STATUSES.map((s) => (
                    <option key={s} value={s}>
                      {s}
                    </option>
                  ))}
                </select>
                {!hideTransfer && (
                  <button className="btn-outline" onClick={() => onTransfer(r.id, actor)}>
                    تحويل للمكتب المندلاوي
                  </button>
                )}
                <button
                  className="btn-soft"
                  onClick={() => onUpdate(r.id, "منجز", actor, "إكمال الإجراءات")}
                >
                  تعليم كمنجز
                </button>
              </div>

              <div className="mt-4">
                <h4 className="text-sm font-semibold mb-2">خط الزمن</h4>
                <ul className="space-y-2">
                  {r.history.map((h, idx) => (
                    <li key={idx} className="text-xs p-2 rounded-xl border bg-slate-50">
                      <div className="flex items-center justify-between">
                        <div className="font-medium">{h.status}</div>
                        <div className="text-[11px] text-slate-500">{fmt(h.at)}</div>
                      </div>
                      <div className="text-[11px] text-slate-600 mt-1">بواسطة: {h.by}</div>
                      {h.note && <div className="text-[11px] text-slate-700 mt-1">ملاحظة: {h.note}</div>}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
}

function RequestCard({ r }) {
  return (
    <div className="rounded-2xl border p-4 shadow-sm">
      <div className="flex items-center justify-between gap-2">
        <div className="font-semibold">{r.title}</div>
        <span className="text-[11px] px-2 py-1 rounded-full border bg-emerald-50 text-emerald-700">
          {r.status}
        </span>
      </div>
      <div className="text-xs text-slate-500 mt-1">رمز الطلب: {r.id}</div>
      <div className="grid grid-cols-2 gap-2 text-sm mt-3">
        <Info label="النوع" value={r.type} />
        <Info label="أُنشئ" value={fmt(r.createdAt)} />
      </div>
      <div className="mt-4">
        <h4 className="text-sm font-semibold mb-2">خط الزمن</h4>
        <ul className="space-y-2">
          {r.history.map((h, idx) => (
            <li key={idx} className="text-xs p-2 rounded-xl border bg-slate-50">
              <div className="flex items-center justify-between">
                <div className="font-medium">{h.status}</div>
                <div className="text-[11px] text-slate-500">{fmt(h.at)}</div>
              </div>
              {h.note && <div className="text-[11px] text-slate-700 mt-1">ملاحظة: {h.note}</div>}
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
}

function Info({ label, value }) {
  return (
    <div className="bg-slate-50 border rounded-xl px-3 py-2">
      <div className="text-[11px] text-slate-500">{label}</div>
      <div className="text-sm font-medium truncate" title={value}>
        {value}
      </div>
    </div>
  );
}

function Field({ label, children }) {
  return (
    <label className="block">
      <div className="text-xs text-slate-600 mb-1">{label}</div>
      {children}
    </label>
  );
}

function Empty({ title, subtitle }) {
  return (
    <div className="text-center p-8 border rounded-2xl bg-slate-50">
      <div className="text-sm font-semibold">{title}</div>
      <div className="text-xs text-slate-500 mt-1">{subtitle}</div>
    </div>
  );
}

// أنماط Tailwind مُختصرة لعناصر متكررة
// ملاحظة: لا حاجة لاستيراد CSS خارجي هنا؛ تُطبّق عبر كلاسات Tailwind
const style = document.createElement("style");
style.innerHTML = `
  .input { @apply w-full rounded-xl border px-3 py-2 text-sm shadow-sm focus:outline-none focus:ring-2 focus:ring-emerald-300; }
  .btn-primary { @apply inline-flex items-center gap-2 rounded-xl bg-emerald-600 text-white text-sm px-4 py-2 shadow hover:bg-emerald-700 transition; }
  .btn-outline { @apply inline-flex items-center gap-2 rounded-xl border text-sm px-3 py-2 hover:bg-emerald-50 transition; }
  .btn-soft { @apply inline-flex items-center gap-2 rounded-xl bg-emerald-50 text-emerald-700 border border-emerald-100 text-sm px-3 py-2 hover:bg-emerald-100 transition; }
`;
document.head.appendChild(style);
